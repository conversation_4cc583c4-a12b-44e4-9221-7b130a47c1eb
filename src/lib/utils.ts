import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatPrice(price: string | number): string {
  const numPrice = typeof price === 'string' ? parseFloat(price) : price
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(numPrice)
}

export function generateWhatsAppMessage(product: { name: string; id: string }, selections: { size: string; quantity: number; color?: string; customerName: string; customerPhone: string; companyName?: string; notes?: string }): string {
  return `Hello IDC Uniform! 👋

I'm interested in:
📦 Product: ${product.name}
🏷️ Product Code: ${product.id}
📏 Size: ${selections.size}
🔢 Quantity: ${selections.quantity}
🎨 Color: ${selections.color || 'Standard'}

Customer Details:
👤 Name: ${selections.customerName}
📱 Phone: ${selections.customerPhone}
🏢 Company: ${selections.companyName || 'Individual'}

Additional Notes:
${selections.notes || 'None'}

Please provide pricing and availability.`
}

export function generateWhatsAppUrl(message: string, phoneNumber: string): string {
  const encodedMessage = encodeURIComponent(message)
  return `https://wa.me/${phoneNumber}?text=${encodedMessage}`
}
